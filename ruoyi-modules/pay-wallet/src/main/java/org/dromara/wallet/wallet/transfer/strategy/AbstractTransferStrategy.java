package org.dromara.wallet.wallet.transfer.strategy;

import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.dromara.wallet.service.IMetaTransferRecService;
import org.dromara.wallet.wallet.transfer.config.TransactionConfirmationConfig;
import org.dromara.wallet.wallet.transfer.dto.*;
import org.dromara.wallet.wallet.transfer.enums.TransactionStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 抽象转账策略基类
 *
 * <p>提取各转账策略的公共逻辑，实现统一的转账流程框架：</p>
 * <ul>
 *   <li>统一的execute()模板方法</li>
 *   <li>统一的异常处理和结果转换</li>
 *   <li>标准化的日志记录格式</li>
 *   <li>公共的参数验证逻辑</li>
 * </ul>
 *
 * <p>子类只需实现核心的转账逻辑，无需关心通用的流程控制</p>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Slf4j
public abstract class AbstractTransferStrategy implements BlockchainTransferStrategy {

    /**
     * 事件发布器
     * 用于发布异步确认事件
     */
    protected ApplicationEventPublisher eventPublisher;

    /**
     * 转账记录服务
     * 用于管理转账记录的创建和更新
     */
    @Autowired(required = false)
    protected IMetaTransferRecService transferRecService;

    /**
     * 各链配置门面
     * 用于获取链配置信息
     */
    @Autowired(required = false)
    protected TronConfigFacade tronConfigFacade;

    /**
     * 设置事件发布器
     * 由子类在构造函数中调用
     *
     * @param eventPublisher 事件发布器
     */
    protected void setEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    /**
     * 模板方法 - 统一转账流程
     *
     * <p>定义了标准的转账执行流程，子类无需覆盖此方法：</p>
     * <ol>
     *   <li>初始化转账结果对象</li>
     *   <li>执行参数验证</li>
     *   <li>调用子类具体转账实现</li>
     *   <li>统一异常处理和结果转换</li>
     *   <li>标记完成并计算耗时</li>
     * </ol>
     */
    @Override
    public final BlockchainTransferResult execute(TransferRequest request) {
        log.info("{}转账策略开始执行: token={}, amount={}, to={}",
            getChainName(), request.getTokenSymbol(), request.getAmount(),
            request.getToAddress().substring(0, Math.min(8, request.getToAddress().length())) + "...");

        // 1. 初始化转账结果对象
        BlockchainTransferResult result = initializeTransferResult(request);

        try {
            // 2. 执行参数验证
            validateTransferRequest(request);

            // 3. 记录转账开始日志
            logTransferStart(request, result.getFromAddress());

            // 4. 调用子类具体转账实现
            executeTransferInternal(request, result);

            // 5. 记录转账成功日志
            logTransferSuccess(result);

        } catch (Exception e) {
            // 6. 统一异常处理 - 使用简洁日志格式
            log.error("{}转账策略执行失败: {}", getChainName(), e.getMessage());
            convertExceptionToResult(e, result);
        }

        // 7. 标记完成并计算耗时
        result.markCompleted();
        return result;
    }

    // ==================== 抽象方法 - 子类必须实现 ====================

    /**
     * 执行具体的转账逻辑
     *
     * <p>子类实现此方法来处理链特定的转账逻辑，包括：</p>
     * <ul>
     *   <li>原生代币 vs 合约代币检测</li>
     *   <li>手续费估算和提供</li>
     *   <li>交易构建和发送</li>
     *   <li>交易确认和验证</li>
     *   <li>设置转账结果</li>
     * </ul>
     *
     * @param request 转账请求
     * @param result  转账结果对象（已初始化，子类需要设置成功状态和交易信息）
     */
    protected abstract void executeTransferInternal(TransferRequest request, BlockchainTransferResult result);

    /**
     * 从私钥推导发送方地址
     *
     * @param privateKey 私钥
     * @return 发送方地址
     */
    protected abstract String deriveFromAddress(String privateKey);

    /**
     * 估算转账手续费
     *
     * <p>子类实现此方法来估算链特定的转账手续费，包括：</p>
     * <ul>
     *   <li>原生代币转账手续费</li>
     *   <li>合约代币转账手续费</li>
     *   <li>链特定的费用组成（如TRON的Energy、Bandwidth）</li>
     * </ul>
     *
     * @param request       转账请求
     * @param isNativeToken 是否为原生代币转账
     * @return 手续费估算结果
     */
    protected abstract FeeEstimate estimateTransferFee(TransferRequest request, boolean isNativeToken);

    /**
     * 检查用户是否需要手续费支持
     *
     * <p>检查用户地址的原生代币余额是否足够支付手续费</p>
     *
     * @param request     转账请求（用于获取链特定信息）
     * @param userAddress 用户地址
     * @param feeEstimate 手续费估算
     * @return true表示需要手续费支持，false表示用户余额充足
     */
    protected abstract boolean needsFeeSupport(TransferRequest request, String userAddress, FeeEstimate feeEstimate);

    /**
     * 提供手续费支持
     *
     * <p>子类实现此方法来提供链特定的手续费支持，可能包括：</p>
     * <ul>
     *   <li>手续费钱包转账</li>
     *   <li>第三方API调用</li>
     *   <li>其他链特定的手续费提供方式</li>
     * </ul>
     *
     * @param request     转账请求（用于获取链特定信息）
     * @param userAddress 用户地址
     * @param feeEstimate 手续费估算
     * @return 手续费提供结果
     */
    protected abstract FeeProvisionResult provideFeeSupport(TransferRequest request, String userAddress, FeeEstimate feeEstimate);

    /**
     * 获取确认配置
     *
     * <p>子类可以覆盖此方法提供链特定的确认配置</p>
     *
     * @param request 转账请求
     * @return 确认配置
     */
    protected TransactionConfirmationConfig getConfirmationConfig(TransferRequest request) {
        // 默认配置：30秒超时，3秒间隔，3次重试
        return TransactionConfirmationConfig.builder()
            .timeoutSeconds(30)
            .checkIntervalSeconds(3)
            .maxRetries(3)
            .requiredConfirmations(1)
            .enableConfirmation(true)
            .confirmationFailureCausesTransferFailure(false)
            .build();
    }

    /**
     * 执行链特定的交易确认逻辑
     *
     * <p>子类必须实现此方法来提供真实的交易确认功能，而不是简化的模拟确认</p>
     *
     * @param txHash 交易哈希
     * @param config 确认配置
     * @return 确认结果
     */
    protected abstract TransactionConfirmationResult confirmTransactionInternal(String txHash, TransactionConfirmationConfig config);


    /**
     * 等待转账确认 - 异步版本（带转账记录ID）
     *
     * <p>发布确认事件而不是同步等待，提高转账响应速度，同时关联转账记录</p>
     *
     * @param txHash           交易哈希
     * @param transferType     转账类型描述
     * @param request          转账请求
     * @param transferRecordId 转账记录ID
     * @return 确认结果（立即返回，实际确认异步进行）
     */
    protected TransactionConfirmationResult waitForTransactionConfirmationAsyncWithRecord(String txHash, String transferType, TransferRequest request, Long transferRecordId) {
        // 1. 获取确认配置
        TransactionConfirmationConfig config = getConfirmationConfig(request);

        // 2. 检查是否启用确认
        if (!config.isEnableConfirmation()) {
            log.debug("{}跳过转账确认: txHash={}, reason=配置禁用", getChainName(), txHash);
            return TransactionConfirmationResult.skipped(txHash, "配置禁用确认");
        }

        // 3. 按照KISS原则，始终执行确认以保证资金安全

        // 4. 调用子类的真实确认逻辑
        try {
            log.info("{}开始确认交易: txHash={}, type={}, recordId={}",
                getChainName(), txHash, transferType, transferRecordId);

            // 调用子类实现的真实确认逻辑
            TransactionConfirmationResult result = confirmTransactionInternal(txHash, config);

            log.info("{}交易确认完成: txHash={}, confirmed={}, confirmations={}",
                getChainName(), txHash, result.isConfirmed(), result.getActualConfirmations());

            return result;

        } catch (Exception e) {
            log.error("{}交易确认失败: txHash={}, error={}",
                getChainName(), txHash, e.getMessage());

            return TransactionConfirmationResult.failure(txHash, TransactionStatus.FAILED, e.getMessage());
        }
    }

    // ==================== 可选覆盖方法 ====================

    /**
     * 验证转账请求参数
     *
     * <p>子类可以覆盖此方法添加链特定的验证逻辑</p>
     *
     * @param request 转账请求
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    protected void validateTransferRequest(TransferRequest request) {
        // 基础参数验证
        if (request == null) {
            throw new IllegalArgumentException("转账请求不能为空");
        }
        if (request.getPrivateKey() == null || request.getPrivateKey().trim().isEmpty()) {
            throw new IllegalArgumentException("私钥不能为空");
        }
        if (request.getToAddress() == null || request.getToAddress().trim().isEmpty()) {
            throw new IllegalArgumentException("接收地址不能为空");
        }
        if (request.getAmount() == null || request.getAmount().trim().isEmpty()) {
            throw new IllegalArgumentException("转账金额不能为空");
        }
        if (request.getTokenSymbol() == null || request.getTokenSymbol().trim().isEmpty()) {
            throw new IllegalArgumentException("代币符号不能为空");
        }
        if (request.getChainName() == null || request.getChainName().trim().isEmpty()) {
            throw new IllegalArgumentException("区块链名称不能为空");
        }

        // 验证金额格式
        try {
            BigDecimal amount = request.getAmountAsBigDecimal();
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new IllegalArgumentException("转账金额必须大于0");
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("转账金额格式无效: " + request.getAmount());
        }
    }

    /**
     * 获取链的原生代币符号
     *
     * <p>子类可以覆盖此方法返回正确的原生代币符号</p>
     *
     * @return 原生代币符号，默认返回链名称
     */
    protected String getNativeTokenSymbol() {
        return getChainName();
    }

    /**
     * 判断是否为原生代币
     *
     * <p>统一的原生代币检测逻辑，基于代币符号比较</p>
     *
     * @param tokenSymbol 代币符号
     * @return true表示是原生代币，false表示是合约代币
     */
    protected final boolean isNativeToken(String tokenSymbol) {
        return getNativeTokenSymbol().equalsIgnoreCase(tokenSymbol);
    }

    // ==================== 统一手续费处理模板方法 ====================

    /**
     * 统一手续费处理流程
     *
     * <p>提供标准化的手续费处理流程，子类可以直接调用：</p>
     * <ol>
     *   <li>估算转账手续费</li>
     *   <li>检查用户是否需要手续费支持</li>
     *   <li>如需要，则提供手续费支持</li>
     *   <li>记录手续费处理结果</li>
     * </ol>
     *
     * @param request 转账请求
     * @return 手续费提供结果
     */
    protected final FeeProvisionResult handleFeeProvision(TransferRequest request) {
        // 1. 判断是否为原生代币
        boolean isNativeToken = isNativeToken(request.getTokenSymbol());

        // 2. 估算手续费
        FeeEstimate feeEstimate = estimateTransferFee(request, isNativeToken);
        log.info("{}手续费估算完成: {}", getChainName(), feeEstimate.getDescription());

        // 3. 检查是否启用手续费钱包
        if (!request.isEnableFeeWallet()) {
            log.info("{}手续费钱包未启用，跳过手续费提供", getChainName());
            return FeeProvisionResult.noProvision();
        }

        // 4. 检查用户是否需要手续费支持
        String fromAddress = deriveFromAddress(request.getPrivateKey());
        if (!needsFeeSupport(request, fromAddress, feeEstimate)) {
            log.info("{}用户余额充足，无需提供手续费", getChainName());
            return FeeProvisionResult.noProvision();
        }

        // 5. 提供手续费支持
        log.info("{}开始提供手续费支持: address={}, needed={}",
            getChainName(), fromAddress.substring(0, Math.min(8, fromAddress.length())) + "...",
            feeEstimate.getDescription());

        FeeProvisionResult result = provideFeeSupport(request, fromAddress, feeEstimate);

        // 6. 记录结果
        if (result.isProvided()) {
            log.info("{}手续费提供成功: {}", getChainName(), result.getDescription());
        } else {
            log.error("{}手续费提供失败: {}", getChainName(),
                result.getErrorMessage() != null ? result.getErrorMessage() : "未知错误");
        }

        return result;
    }

    // ==================== 公共工具方法 ====================

    /**
     * 初始化转账结果对象
     */
    private BlockchainTransferResult initializeTransferResult(TransferRequest request) {
        // 从私钥推导发送方地址
        String fromAddress = deriveFromAddress(request.getPrivateKey());

        return BlockchainTransferResult.builder()
            .chainName(getChainName().toUpperCase())
            .transferAmount(request.getAmountAsBigDecimal())
            .transferTokenSymbol(request.getTokenSymbol())
            .fromAddress(fromAddress)
            .toAddress(request.getToAddress())
            .requestId(request.getRequestId())
            .startTime(LocalDateTime.now())
            .build();
    }

    /**
     * 记录转账开始日志
     */
    private void logTransferStart(TransferRequest request, String fromAddress) {
        log.info("{}转账开始: from={}, to={}, token={}, amount={}",
            getChainName(),
            fromAddress.substring(0, Math.min(8, fromAddress.length())) + "...",
            request.getToAddress().substring(0, Math.min(8, request.getToAddress().length())) + "...",
            request.getTokenSymbol(),
            request.getAmountAsBigDecimal());
    }

    /**
     * 记录转账成功日志
     */
    private void logTransferSuccess(BlockchainTransferResult result) {
        log.info("{}转账策略执行成功: txHash={}, feeProvided={}, executionTime={}ms",
            getChainName(),
            result.getTxHash(),
            result.isFeeProvided(),
            result.getExecutionTimeMs());
    }

    /**
     * 统一的异常转换为结果
     *
     * <p>将各种异常转换为统一的错误结果，子类可以覆盖此方法添加链特定的错误处理</p>
     */
    protected void convertExceptionToResult(Exception e, BlockchainTransferResult result) {
        result.setSuccess(false);
        result.setErrorMessage(e.getMessage());

        // 根据异常类型设置错误代码
        String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

        if (message.contains("insufficient") || message.contains("余额不足") || message.contains("balance")) {
            result.setErrorCode("INSUFFICIENT_BALANCE");
        } else if (message.contains("invalid") || message.contains("无效") || message.contains("格式")) {
            result.setErrorCode("INVALID_PARAMETER");
        } else if (message.contains("timeout") || message.contains("超时")) {
            result.setErrorCode("TRANSACTION_TIMEOUT");
        } else if (message.contains("network") || message.contains("网络") || message.contains("connection")) {
            result.setErrorCode("NETWORK_ERROR");
        } else if (message.contains("gas") || message.contains("fee") || message.contains("手续费")) {
            result.setErrorCode("FEE_ERROR");
        } else {
            result.setErrorCode("UNKNOWN_ERROR");
        }
    }
}
